#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试大文件上传的脚本 - 专门针对网页代码.html (1.4MB)
用于诊断和解决API超时问题
"""

import requests
import time
import os
from requests_toolbelt.multipart.encoder import MultipartEncoder
import threading

def test_large_file_upload_with_timeout_handling():
    """测试大文件上传，包含超时处理和重试机制"""
    
    html_file = "网页代码.html"
    api_url = "http://localhost:5000/api/scrape"
    
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return
    
    file_size = os.path.getsize(html_file)
    print(f"📁 文件大小: {file_size} 字节 ({file_size/1024/1024:.2f} MB)")
    
    # 读取文件内容
    print("📖 读取文件内容...")
    with open(html_file, 'r', encoding='utf-8') as f:
        html_content = f.read()
    
    print(f"✅ 文件读取完成，内容长度: {len(html_content)} 字符")
    
    # 测试不同的超时设置
    timeout_configs = [
        {"connect": 10, "read": 30, "description": "标准超时 (10s连接, 30s读取)"},
        {"connect": 15, "read": 60, "description": "中等超时 (15s连接, 60s读取)"},
        {"connect": 30, "read": 120, "description": "长超时 (30s连接, 120s读取)"},
    ]
    
    for i, config in enumerate(timeout_configs, 1):
        print(f"\n{'='*60}")
        print(f"🧪 测试 {i}/{len(timeout_configs)}: {config['description']}")
        print(f"{'='*60}")
        
        success = test_upload_with_config(html_content, api_url, config)
        
        if success:
            print(f"✅ 测试成功！最佳配置: {config['description']}")
            break
        else:
            print(f"❌ 测试失败，尝试下一个配置...")
    
    print(f"\n{'='*60}")
    print("🔧 如果所有测试都失败，请检查:")
    print("1. Flask应用是否正在运行 (python amazon_scraper.py)")
    print("2. 是否有防火墙或代理阻止连接")
    print("3. 服务器是否有足够的内存处理1.4MB文件")
    print("4. 是否有其他进程占用端口5000")
    print(f"{'='*60}")

def test_upload_with_config(html_content, api_url, timeout_config):
    """使用指定配置测试上传"""
    
    try:
        # 准备multipart数据
        print("📦 准备multipart数据...")
        
        # 使用requests-toolbelt进行更好的multipart处理
        multipart_data = MultipartEncoder(
            fields={
                'pageContent': ('webpage.html', html_content, 'text/html'),
                'warehouse_id': '3079',
                'source_link': 'https://amazon.de/dp/TEST123',
                'send_to_api': 'false',  # 不发送到WMS API
                'enable_variants': 'true',
                'verbose_debug': 'true',
                'price': '29.99'
            }
        )
        
        print(f"📊 multipart数据大小: {len(multipart_data.read())} 字节")
        multipart_data = MultipartEncoder(
            fields={
                'pageContent': ('webpage.html', html_content, 'text/html'),
                'warehouse_id': '3079',
                'source_link': 'https://amazon.de/dp/TEST123',
                'send_to_api': 'false',
                'enable_variants': 'true',
                'verbose_debug': 'true',
                'price': '29.99'
            }
        )
        
        # 设置请求头
        headers = {
            'Content-Type': multipart_data.content_type,
            'User-Agent': 'Large File Upload Test/1.0'
        }
        
        print(f"🚀 开始上传...")
        start_time = time.time()
        
        # 创建进度监控
        progress_thread = threading.Thread(
            target=monitor_upload_progress, 
            args=(start_time, timeout_config['read'])
        )
        progress_thread.daemon = True
        progress_thread.start()
        
        # 发送请求
        response = requests.post(
            api_url,
            data=multipart_data,
            headers=headers,
            timeout=(timeout_config['connect'], timeout_config['read']),
            stream=False  # 不使用流式传输，一次性发送
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n📊 上传完成!")
        print(f"⏱️  总耗时: {total_time:.2f}秒")
        print(f"📡 HTTP状态码: {response.status_code}")
        print(f"📏 响应大小: {len(response.text)} 字节")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"✅ 请求成功")
                print(f"🎯 数据提取成功: {result.get('success', False)}")
                
                # 检查性能信息
                debug_info = result.get('debug_info', '')
                if debug_info:
                    performance_lines = [line for line in debug_info.split('\n') if '[PERFORMANCE]' in line]
                    if performance_lines:
                        print(f"🚀 性能信息:")
                        for line in performance_lines[:5]:  # 显示前5条
                            print(f"   {line}")
                
                # 检查提取的数据
                data_info = result.get('data', {})
                if data_info:
                    print(f"📋 提取结果:")
                    print(f"   标题: {data_info.get('title', 'N/A')[:50]}...")
                    print(f"   价格: {data_info.get('price', 'N/A')}")
                    print(f"   图片: {len(data_info.get('images', []))} 张")
                    print(f"   变体: {len(data_info.get('variants', []))} 个")
                
                return True
                
            except Exception as e:
                print(f"❌ 响应解析失败: {e}")
                print(f"📄 响应内容前500字符: {response.text[:500]}")
                return False
        else:
            print(f"❌ HTTP请求失败")
            print(f"📄 错误响应: {response.text[:500]}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print(f"❌ 连接超时 (>{timeout_config['connect']}秒)")
        return False
        
    except requests.exceptions.ReadTimeout:
        print(f"❌ 读取超时 (>{timeout_config['read']}秒)")
        return False
        
    except requests.exceptions.Timeout:
        print(f"❌ 请求超时")
        return False
        
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def monitor_upload_progress(start_time, max_time):
    """监控上传进度"""
    while True:
        elapsed = time.time() - start_time
        if elapsed >= max_time:
            break
            
        progress = (elapsed / max_time) * 100
        print(f"\r⏳ 上传进度: {elapsed:.1f}s / {max_time}s ({progress:.1f}%)", end='', flush=True)
        time.sleep(1)

def test_api_health():
    """测试API健康状态"""
    try:
        response = requests.get("http://localhost:5000/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常运行")
            return True
        else:
            print(f"⚠️ API服务响应异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到API服务: {e}")
        return False

if __name__ == "__main__":
    print("🔧 大文件上传测试工具")
    print("=" * 60)
    
    # 检查API健康状态
    if test_api_health():
        test_large_file_upload_with_timeout_handling()
    else:
        print("\n💡 解决建议:")
        print("1. 启动API服务: python amazon_scraper.py")
        print("2. 检查端口5000是否被占用")
        print("3. 确认防火墙设置")
