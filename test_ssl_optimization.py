#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
SSL连接优化测试脚本
专门用于解决Amazon变体价格获取的SSL错误问题
"""

import os
import sys
import subprocess
import time

def test_ssl_optimized_processing():
    """测试SSL优化后的处理"""
    
    html_file = "网页代码.html"
    
    print("🔧 Amazon Scraper SSL优化测试")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return False
    
    # 获取文件信息
    file_size = os.path.getsize(html_file)
    print(f"📁 文件: {html_file}")
    print(f"📊 大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    
    print("\n🚀 开始SSL优化测试...")
    print("优化内容:")
    print("  ✅ 减少批次大小 (15 -> 8 ASIN)")
    print("  ✅ 增加SSL错误重试时间")
    print("  ✅ 更新User-Agent到最新版本")
    print("  ✅ 优化请求头以减少检测")
    print("  ✅ 改进SSL连接配置")
    
    # 执行优化后的处理
    start_time = time.time()
    
    try:
        # 执行命令
        result = subprocess.run(
            ["python3", "amazon_scraper.py", "--html", html_file, "--no-api"],
            capture_output=True,
            text=True,
            timeout=600,  # 10分钟超时
            cwd=os.getcwd()
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"\n⏱️  总执行时间: {execution_time:.2f}秒")
        print(f"🔢 返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 处理成功!")
            
            # 分析输出中的关键信息
            output_lines = result.stdout.split('\n')
            
            # 统计变体价格获取情况
            ssl_errors = 0
            price_success = 0
            price_failed = 0
            
            for line in output_lines:
                if "SSL" in line and ("错误" in line or "Error" in line):
                    ssl_errors += 1
                elif "获取价格成功" in line:
                    price_success += 1
                elif "批量获取变体价格最终失败" in line:
                    price_failed += 1
            
            print(f"\n📊 变体价格获取统计:")
            print(f"  ✅ 成功获取: {price_success} 个")
            print(f"  ❌ 获取失败: {price_failed} 个")
            print(f"  🔒 SSL错误: {ssl_errors} 次")
            
            # 显示关键成功信息
            success_lines = []
            for line in output_lines:
                if any(keyword in line for keyword in [
                    '产品标题:', '产品价格:', '变体数量:', '图片数量:', 
                    '✅', '成功', '完成'
                ]):
                    success_lines.append(line.strip())
            
            if success_lines:
                print(f"\n📋 处理结果:")
                for line in success_lines[:8]:  # 显示前8条关键信息
                    print(f"   {line}")
            
            # 检查是否有SSL改进
            if ssl_errors == 0:
                print(f"\n🎉 SSL优化成功! 没有发生SSL连接错误")
            elif ssl_errors < 5:
                print(f"\n✅ SSL优化有效! SSL错误减少到 {ssl_errors} 次")
            else:
                print(f"\n⚠️ SSL错误仍然较多 ({ssl_errors} 次)，可能需要进一步优化")
            
            return True
            
        else:
            print("❌ 处理失败!")
            
            # 分析错误信息
            if result.stderr:
                error_lines = result.stderr.split('\n')
                ssl_related_errors = [line for line in error_lines if "SSL" in line or "EOF" in line]
                
                if ssl_related_errors:
                    print(f"\n🔒 SSL相关错误:")
                    for line in ssl_related_errors[:3]:  # 显示前3个SSL错误
                        print(f"   {line.strip()}")
                    
                    print(f"\n💡 SSL错误解决建议:")
                    print(f"   1. 检查网络连接稳定性")
                    print(f"   2. 尝试使用VPN或代理")
                    print(f"   3. 检查防火墙设置")
                    print(f"   4. 稍后重试（Amazon可能临时限制）")
                else:
                    print(f"\n🚨 其他错误:")
                    for line in error_lines[:5]:
                        if line.strip():
                            print(f"   {line.strip()}")
            
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 处理超时 (10分钟)")
        print("💡 建议: 可能网络连接问题或Amazon限制较严")
        return False
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def show_ssl_optimization_tips():
    """显示SSL优化建议"""
    print("\n📚 SSL连接优化建议:")
    print("-" * 50)
    print("1. 网络环境优化:")
    print("   • 使用稳定的网络连接")
    print("   • 避免使用不稳定的WiFi")
    print("   • 考虑使用有线网络")
    print()
    print("2. 代理和VPN:")
    print("   • 使用高质量的代理服务")
    print("   • 选择德国或欧洲的VPN节点")
    print("   • 避免使用免费代理")
    print()
    print("3. 时间策略:")
    print("   • 避免在Amazon高峰时段请求")
    print("   • 分批处理大量变体")
    print("   • 在请求间增加适当延迟")
    print()
    print("4. 技术优化:")
    print("   • 使用最新的TLS版本")
    print("   • 更新Python和requests库")
    print("   • 优化请求头和User-Agent")
    print("-" * 50)

def check_ssl_environment():
    """检查SSL环境"""
    print("🔍 SSL环境检查:")
    print("-" * 30)
    
    try:
        import ssl
        print(f"🔒 SSL版本: {ssl.OPENSSL_VERSION}")
        print(f"🔒 TLS版本: {ssl.TLSVersion.TLSv1_2.name} 支持")
        
        # 检查证书验证
        import requests
        try:
            response = requests.get("https://www.amazon.de", timeout=10, verify=True)
            print(f"✅ Amazon.de SSL连接: 正常 (状态码: {response.status_code})")
        except Exception as e:
            print(f"❌ Amazon.de SSL连接: 失败 ({e})")
            
    except Exception as e:
        print(f"❌ SSL环境检查失败: {e}")
    
    print("-" * 30)

def main():
    print("🚀 Amazon Scraper SSL优化测试工具")
    
    # 检查SSL环境
    check_ssl_environment()
    
    # 显示优化建议
    show_ssl_optimization_tips()
    
    # 询问是否运行测试
    run_test = input("\n是否运行SSL优化测试? (y/n): ").lower().strip()
    
    if run_test == 'y':
        success = test_ssl_optimized_processing()
        
        if success:
            print(f"\n🎯 测试完成! SSL优化生效")
            print(f"💡 如果仍有SSL错误，请:")
            print(f"   1. 检查网络环境")
            print(f"   2. 尝试使用代理")
            print(f"   3. 稍后重试")
        else:
            print(f"\n❌ 测试失败，需要进一步优化")
            print(f"💡 建议:")
            print(f"   1. 检查网络连接")
            print(f"   2. 更新Python环境")
            print(f"   3. 使用VPN或代理")
    else:
        print("\n💡 您可以直接运行:")
        print("python3 amazon_scraper.py --html 网页代码.html --no-api")

if __name__ == "__main__":
    main()
