#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
批次优化测试脚本
专门用于验证批次1和批次2的差异优化效果
"""

import os
import sys
import subprocess
import time
import re

def analyze_batch_results(output_text):
    """分析批次处理结果"""
    
    batch_results = {}
    lines = output_text.split('\n')
    
    current_batch = None
    
    for line in lines:
        # 检测批次开始
        batch_match = re.search(r'批量获取变体价格: (\d+)个ASIN \(批次 (\d+)\)', line)
        if batch_match:
            current_batch = int(batch_match.group(2))
            batch_results[current_batch] = {
                'asin_count': int(batch_match.group(1)),
                'status': 'unknown',
                'errors': [],
                'successes': 0,
                'ssl_errors': 0,
                'http_errors': []
            }
            continue
        
        if current_batch is not None:
            # 检测成功
            if '请求成功，响应长度:' in line:
                batch_results[current_batch]['status'] = 'success'
            
            # 检测SSL错误
            elif 'SSL' in line and ('错误' in line or 'Error' in line):
                batch_results[current_batch]['ssl_errors'] += 1
                batch_results[current_batch]['errors'].append(line.strip())
                if batch_results[current_batch]['status'] == 'unknown':
                    batch_results[current_batch]['status'] = 'ssl_error'
            
            # 检测HTTP错误
            elif re.search(r'状态码:\s*(4\d\d|5\d\d)', line):
                status_match = re.search(r'状态码:\s*(\d+)', line)
                if status_match:
                    status_code = status_match.group(1)
                    batch_results[current_batch]['http_errors'].append(status_code)
                    batch_results[current_batch]['errors'].append(line.strip())
                    if batch_results[current_batch]['status'] == 'unknown':
                        batch_results[current_batch]['status'] = f'http_{status_code}'
            
            # 检测价格获取成功
            elif '获取价格成功' in line:
                batch_results[current_batch]['successes'] += 1
    
    return batch_results

def test_batch_optimization():
    """测试批次优化效果"""
    
    html_file = "网页代码.html"
    
    print("🔧 Amazon Scraper 批次优化测试")
    print("=" * 60)
    
    # 检查文件是否存在
    if not os.path.exists(html_file):
        print(f"❌ 文件 {html_file} 不存在")
        return False
    
    # 获取文件信息
    file_size = os.path.getsize(html_file)
    print(f"📁 文件: {html_file}")
    print(f"📊 大小: {file_size:,} 字节 ({file_size/1024/1024:.2f} MB)")
    
    print("\n🚀 开始批次优化测试...")
    print("优化内容:")
    print("  ✅ 第一批次使用中间ASIN作为主ASIN")
    print("  ✅ 第一批次预热连接")
    print("  ✅ 第一批次后增加稳定延迟")
    print("  ✅ 智能批次间延迟策略")
    
    # 执行优化后的处理
    start_time = time.time()
    
    try:
        # 执行命令
        result = subprocess.run(
            ["python3", "amazon_scraper.py", "--html", html_file, "--no-api"],
            capture_output=True,
            text=True,
            timeout=600,  # 10分钟超时
            cwd=os.getcwd()
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"\n⏱️  总执行时间: {execution_time:.2f}秒")
        print(f"🔢 返回码: {result.returncode}")
        
        if result.returncode == 0:
            print("✅ 处理成功!")
            
            # 分析批次结果
            batch_results = analyze_batch_results(result.stdout)
            
            if batch_results:
                print(f"\n📊 批次处理分析:")
                print("-" * 50)
                
                for batch_num in sorted(batch_results.keys()):
                    batch_info = batch_results[batch_num]
                    status_icon = "✅" if batch_info['status'] == 'success' else "❌"
                    
                    print(f"{status_icon} 批次 {batch_num}:")
                    print(f"   ASIN数量: {batch_info['asin_count']}")
                    print(f"   状态: {batch_info['status']}")
                    print(f"   成功获取价格: {batch_info['successes']} 个")
                    
                    if batch_info['ssl_errors'] > 0:
                        print(f"   SSL错误: {batch_info['ssl_errors']} 次")
                    
                    if batch_info['http_errors']:
                        print(f"   HTTP错误: {', '.join(batch_info['http_errors'])}")
                    
                    print()
                
                # 对比分析
                if len(batch_results) >= 2:
                    batch1 = batch_results.get(1, {})
                    batch2 = batch_results.get(2, {})
                    
                    print("🔍 批次对比分析:")
                    print("-" * 30)
                    
                    if batch1.get('status') == 'success' and batch2.get('status') == 'success':
                        print("🎉 优化成功! 批次1和批次2都成功了")
                    elif batch1.get('status') != 'success' and batch2.get('status') == 'success':
                        print("⚠️ 批次1仍然失败，但批次2成功")
                        print("💡 建议: 可能需要进一步优化第一批次策略")
                    elif batch1.get('status') == 'success' and batch2.get('status') != 'success':
                        print("✅ 批次1优化成功! 现在批次1也能正常工作了")
                    else:
                        print("❌ 两个批次都失败，需要检查网络或其他问题")
                    
                    # SSL错误对比
                    batch1_ssl = batch1.get('ssl_errors', 0)
                    batch2_ssl = batch2.get('ssl_errors', 0)
                    
                    if batch1_ssl == 0 and batch2_ssl == 0:
                        print("🔒 SSL优化完全成功! 没有SSL错误")
                    elif batch1_ssl < batch2_ssl:
                        print(f"🔒 批次1 SSL错误减少: {batch1_ssl} vs {batch2_ssl}")
                    elif batch1_ssl > batch2_ssl:
                        print(f"🔒 批次2 SSL错误更少: {batch1_ssl} vs {batch2_ssl}")
            
            return True
            
        else:
            print("❌ 处理失败!")
            
            # 分析错误信息
            if result.stderr:
                print(f"\n🚨 错误信息:")
                error_lines = result.stderr.split('\n')[:10]
                for line in error_lines:
                    if line.strip():
                        print(f"   {line.strip()}")
            
            return False
            
    except subprocess.TimeoutExpired:
        print("⏰ 处理超时 (10分钟)")
        return False
        
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False

def show_batch_optimization_summary():
    """显示批次优化总结"""
    print("\n📚 批次优化策略总结:")
    print("-" * 50)
    print("🎯 问题分析:")
    print("   • 批次1经常出现443/503错误")
    print("   • 批次2通常能正常处理")
    print("   • 可能原因: 第一个ASIN被限制、连接未预热")
    print()
    print("🚀 优化措施:")
    print("   1. 智能ASIN选择:")
    print("      - 第一批次使用中间ASIN作为主ASIN")
    print("      - 避免使用可能被限制的第一个ASIN")
    print()
    print("   2. 连接预热:")
    print("      - 第一批次前增加1秒预热延迟")
    print("      - 让SSL连接稳定建立")
    print()
    print("   3. 智能延迟:")
    print("      - 第一批次后延迟2-3秒")
    print("      - 后续批次延迟0.8-1.5秒")
    print()
    print("   4. 错误处理:")
    print("      - SSL错误增加重试时间")
    print("      - 详细的错误分析和建议")
    print("-" * 50)

def main():
    print("🚀 Amazon Scraper 批次优化测试工具")
    
    # 显示优化总结
    show_batch_optimization_summary()
    
    # 询问是否运行测试
    run_test = input("\n是否运行批次优化测试? (y/n): ").lower().strip()
    
    if run_test == 'y':
        success = test_batch_optimization()
        
        if success:
            print(f"\n🎯 测试完成!")
            print(f"💡 如果批次1仍然失败，建议:")
            print(f"   1. 检查网络环境")
            print(f"   2. 尝试使用代理")
            print(f"   3. 调整ASIN选择策略")
        else:
            print(f"\n❌ 测试失败")
            print(f"💡 建议检查网络连接和Amazon访问状态")
    else:
        print("\n💡 您可以直接运行:")
        print("python3 amazon_scraper.py --html 网页代码.html --no-api")

if __name__ == "__main__":
    main()
