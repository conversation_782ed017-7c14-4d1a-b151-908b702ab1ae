[uwsgi]
# 项目目录
chdir=/www/wwwroot/**************/amaon/ifyapi

# 指定项目application
wsgi-file=/www/wwwroot/**************/amaon/ifyapi/app.py

# python 程序内用以启动的application 变量名
callable=application

# ============= 针对大文件处理优化的进程配置 =============
# 进程个数 - 减少进程数，避免资源竞争
processes=8

# 线程个数 - 适中的线程数，平衡并发和资源使用
threads=5

# 启用主进程
master=true

# ============= 大文件处理优化配置 =============
# 设置缓冲区大小 - 增加到2MB以支持1.4MB HTML文件
buffer-size = 2097152

# 设置最大请求大小 - 允许100MB请求
limit-post = 104857600

# 设置请求体缓冲区大小
post-buffering = 8192

# 设置请求头缓冲区大小
buffer-size = 65536

# ============= 超时配置优化 =============
# HTTP连接超时 - 增加到60秒
http-timeout = 60

# Socket超时 - 增加到120秒
socket-timeout = 120

# 请求超时 - 增加到180秒（3分钟）
harakiri = 180

# 慢请求日志记录 - 记录超过30秒的请求
log-slow = 30000

# ============= 内存和性能优化 =============
# 启用内存报告
memory-report = true

# 设置最大内存使用 - 每个worker最大512MB
limit-as = 536870912

# 启用垃圾回收
vacuum = true

# 请求处理完后重置Python解释器
reload-on-as = 512

# 每个worker处理5000个请求后重启（防止内存泄漏）
max-requests = 5000

# 添加随机性避免同时重启
max-requests-delta = 1000

# ============= 日志和监控优化 =============
# 后台运行,并输出日志
daemonize = /www/wwwlogs/python/ifyapi/uwsgi.log

# 启用详细日志
log-date = true
log-prefix = [uWSGI-ifyapi]

# 记录慢请求
log-slow = 10000

# 记录大请求
log-big = 1048576

# ============= 网络配置 =============
# 指定ip及端口
http=0.0.0.0:1122

# 启用HTTP 1.1长连接
http-keepalive = true

# 设置监听队列大小
listen = 1024

# ============= 安全配置 =============
# 启动uwsgi的用户名和用户组
uid=www
gid=www

# 指定启动时的pid文件路径
pidfile=/www/wwwroot/**************/amaon/ifyapi/uwsgi.pid

# 禁用不必要的功能
disable-logging = false
ignore-sigpipe = true
ignore-write-errors = true

# ============= 特殊优化配置 =============
# 启用thunder lock（避免惊群效应）
thunder-lock = true

# 启用单一解释器模式（提高性能）
single-interpreter = true

# 启用主线程
enable-threads = true

# 预分叉模式优化
lazy-apps = true

# ============= 监控和统计 =============
# 启用统计服务器（可选，用于监控）
# stats = 127.0.0.1:9191
# stats-http = true

# ============= 自定义Python路径（如果需要） =============
# pythonpath = /www/wwwroot/**************/amaon/ifyapi
# home = /www/server/panel/pyenv/versions/3.8.18

# ============= 调试配置（生产环境建议关闭） =============
# 显示配置
# show-config = true

# 启用Python调用跟踪（仅调试时使用）
# py-call-osafterfork = true
