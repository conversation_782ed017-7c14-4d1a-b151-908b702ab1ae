# 大文件API超时问题解决方案

## 🔍 问题分析

### **现象**
- 使用API发送 `网页代码.html` (1.4MB) 经常超时
- 直接运行脚本 `python amazon_scraper.py` 正常工作

### **根本原因**

#### ✅ **直接运行脚本**（正常）:
```python
# 第5572行：自动检测本地HTML文件
html_file = "网页代码.html"
with open(html_file, 'r', encoding='utf-8') as f:
    html_content = f.read()  # 直接文件读取，~0.01秒
```

#### ❌ **API调用**（超时）:
1. **HTTP传输**: 1.4MB数据网络传输
2. **multipart解析**: Flask解析multipart/form-data
3. **内存处理**: 大文件内存缓存和处理
4. **处理管道**: 完整HTTP请求处理流程

### **文件信息**
- 大小: 1,403,291 字节 (1.4MB)
- 格式: 完整Amazon产品页面HTML
- 内容: 大量CSS、JavaScript、产品数据

## 🚀 解决方案

### **已实施的优化**

#### **1. Flask应用配置优化**
```python
# 增加超时和性能配置
app.config['MAX_CONTENT_LENGTH'] = 100 * 1024 * 1024  # 100MB
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 0  # 禁用缓存
app.config['PERMANENT_SESSION_LIFETIME'] = 300  # 5分钟会话超时
app.config['UPLOAD_FOLDER'] = tempfile.gettempdir()  # 临时目录
```

#### **2. 大文件分块读取**
```python
# 针对>1MB文件使用分块读取
if file_size > 1024 * 1024:
    chunks = []
    chunk_size = 64 * 1024  # 64KB chunks
    while True:
        chunk = found_file.read(chunk_size)
        if not chunk:
            break
        chunks.append(chunk)
    raw_html_bytes = b''.join(chunks)
```

#### **3. 请求监控和预警**
```python
# 大文件预警系统
if size_mb > 1.0:
    print(f"[INFO] 检测到大文件请求 ({size_mb:.2f} MB)，启用优化处理模式")
if size_mb > 5.0:
    print(f"[WARNING] 文件过大 ({size_mb:.2f} MB)，可能需要较长处理时间")
```

#### **4. Flask服务优化启动**
```python
app.run(
    host='0.0.0.0',
    port=5000,
    debug=False,      # 生产模式
    threaded=True,    # 多线程处理
    use_reloader=False # 禁用重载器
)
```

### **测试和验证**

#### **使用测试脚本**
```bash
# 安装依赖
pip install requests-toolbelt

# 运行大文件上传测试
python test_large_file_upload.py
```

#### **测试配置**
- 标准超时: 10s连接 + 30s读取
- 中等超时: 15s连接 + 60s读取  
- 长超时: 30s连接 + 120s读取

## 🔧 使用建议

### **1. 启动优化后的API服务**
```bash
python amazon_scraper.py
```

输出应显示:
```
🚀 启动Amazon Scraper API服务...
📡 API地址: http://localhost:5000
⚠️  针对大文件上传进行了优化 (支持1.4MB+ HTML文件)
```

### **2. 客户端超时设置**
```python
# 推荐的客户端超时设置
timeout = (30, 120)  # 30s连接，120s读取

response = requests.post(
    'http://localhost:5000/api/scrape',
    files={'pageContent': open('网页代码.html', 'rb')},
    data={
        'warehouse_id': '3079',
        'send_to_api': 'false',
        'verbose_debug': 'true'
    },
    timeout=timeout
)
```

### **3. 监控性能指标**
关注日志中的性能信息:
```
[PERFORMANCE] 文件处理耗时: 0.156秒
[PERFORMANCE] 数据解析耗时: 0.012秒  
[PERFORMANCE] API总处理时间: 4.23秒
[SUCCESS] API处理时间优化成功: 4.23秒
```

## ⚠️ 故障排除

### **如果仍然超时**

#### **1. 检查系统资源**
```bash
# 检查内存使用
tasklist /fi "imagename eq python.exe"

# 检查端口占用
netstat -an | findstr :5000
```

#### **2. 增加客户端超时**
```python
# 更长的超时设置
timeout = (60, 300)  # 1分钟连接，5分钟读取
```

#### **3. 使用分段上传**
```python
# 将大文件分成多个小块上传
def upload_in_chunks(file_path, chunk_size=512*1024):
    with open(file_path, 'rb') as f:
        while True:
            chunk = f.read(chunk_size)
            if not chunk:
                break
            # 上传chunk
```

#### **4. 检查网络环境**
- 防火墙设置
- 代理配置
- 网络延迟

### **性能基准**
- **目标处理时间**: < 10秒
- **文件读取**: < 0.2秒
- **数据解析**: < 0.1秒
- **HTML处理**: 2-8秒（取决于内容复杂度）

## 📊 预期改进效果

### **优化前**
- 经常超时（>30秒或失败）
- 内存使用峰值高
- 处理不稳定

### **优化后**  
- 处理时间: 4-10秒
- 内存使用平稳
- 支持1.4MB+文件
- 错误处理完善

通过这些优化，您的1.4MB HTML文件应该能够稳定地通过API处理，不再出现超时问题。
